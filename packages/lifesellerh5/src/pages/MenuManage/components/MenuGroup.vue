<template>
  <div class="menu-group-edit" :data-group-id="group.groupId">
    <!-- 分组头部 -->
    <div class="group-header-wrapper">

      <div class="group-header">
        <div class="group-info">
          <span class="group-name">{{ group.groupName }}</span>
          <OnixIcon v-if="isSpecialty" class="group-info-icon" icon="detail" size="16" @click="handleClickInfo" />
        </div>
        <div class="group-actions">
          <span
            v-if="!isSpecialty"
            class="action-btn"
            @click="onEditName"
          >
            改名
          </span>
          <Divider v-if="!isSpecialty && showDeleteBtn" direction="vertical" :size="14"></Divider>
          <span
            v-if="!isSpecialty && showDeleteBtn"
            class="action-btn"
            @click="onDelete"
          >
            删除
          </span>
        </div>
      </div>
    </div>
    <!-- 菜品列表 -->
    <div class="dishes-list" :class="{ 'specialty-dishes': isSpecialty }">
      <draggable
        v-model="localDishes"
        :group="dragGroup"
        item-key="dishId"
        handle=".dish-drag-handle"
        :animation="150"
        :delay="50"
        :delay-on-touch-start="true"
        :touch-start-threshold="5"
        :force-fallback="false"
        ghost-class="dish-ghost"
        chosen-class="dish-chosen"
        drag-class="dish-drag"
        @change="onDishesChange"
        @start="onDragStart"
        @end="onDragEnd"
        @add="onAdd"
        @remove="onRemove"
      >
        <template #item="{ element: dish }">
          <div class="dish-item-wrapper" :data-dish-id="dish.dishId">
            <DishItem
              :dish="dish"
              :in-specialty-group="isSpecialty"
              @edit="onEditDish"
              @delete="onDeleteDish"
            />
          </div>
        </template>
      </draggable>
      <!-- 添加菜品按钮 -->
      <div v-if="!isSpecialty" class="add-dish-btn" @click="onAddDish">
        <OnixIcon icon="add_circle" size="16" />
        <span class="add-dish-btn-text">增加菜品</span>
      </div>
    </div>
    <AlertMethod ref="alertMethodRef" />
  </div>
</template>

<script lang="ts" setup>
  import {
    ref,
    computed,
    watch,
    toRefs
  } from 'vue'
  import { Divider } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import draggable from 'vuedraggable'
  import DishItem from './DishItem.vue'
  import AlertMethod from './AlertMethod.vue'
  import { IMenuGroupListExtended, IDishList } from '~/types/menu'
  import '~/assets/svg/detail.svg'
  import '~/assets/svg/add_circle.svg'

  interface Props {
    group: IMenuGroupListExtended
    index: number
    totalGroups: number
    specialtyDishCount: number
    isSpecialty: boolean
  }

  const props = defineProps<Props>()
  const emit = defineEmits(['updateGroup', 'deleteGroup', 'addDish', 'editDish', 'deleteDish', 'sortDishes', 'renameGroup', 'crossGroupDrag'])
  const alertMethodRef = ref<InstanceType<typeof AlertMethod>>()
  // 解构props以便在模板中直接使用，使用toRefs保持响应性
  const { group } = toRefs(props)

  const localDishes = ref<IDishList[]>([])

  // 是否显示删除按钮 - 非招牌菜分组且分组数大于2
  const showDeleteBtn = computed(() => props.totalGroups > 1)

  // 拖拽组配置
  const dragGroup = computed(() => {
    return {
      name: 'dishes', // 统一使用同一个组名，允许跨组拖拽
      put: true,
      pull: true
    }
  })

  // 监听dishes变化
  watch(() => props.group.dishList, newDishes => {
    localDishes.value = [...newDishes]
  }, { immediate: true, deep: true })

  // 编辑分组名称 - 触发父组件的改名事件
  const onEditName = () => {
    if (props.isSpecialty) return
    emit('renameGroup', props.group.groupId)
  }

  const handleClickInfo = () => {
    alertMethodRef.value?.showAlert({
      title: '招牌菜分组',
      message: `
      •招牌菜组不可改名，不可跟其他分组自定义排序，保持置顶\n
      • 组内菜品可自由排序\n
      •菜品为空时，招牌菜组自动删除`,
      confirmText: '我知道了',
      showCancelButton: false
    })
  }

  const onDelete = () => {
    emit('deleteGroup', props.group.groupId)
  }

  const onAddDish = () => {
    emit('addDish', props.group.groupId)
  }

  const onEditDish = (dish: IDishList) => {
    // 只传递菜品数据
    emit('editDish', dish)
  }

  const onDeleteDish = (dish: IDishList) => {
    emit('deleteDish', props.group.groupId, dish.dishId)
  }

  const onDishesChange = () => {
    emit('sortDishes', props.group.groupId, localDishes.value)
  }

  const onAdd = (evt: any) => {
    // 跨组拖拽时的添加事件，仅普通分组间可用
    console.log('onAdd', evt)
  }

  const onRemove = (evt: any) => {
    // 跨组拖拽时的移除事件，仅普通分组间可用
    console.log('onRemove', evt)
  }

  let isDragging = false

  const onDragStart = (evt: any) => {
    isDragging = true

    // 使用事件监听器阻止滚动
    const preventTouchScroll = (e: TouchEvent) => {
      if (isDragging) {
        e.preventDefault()
      }
    }

    const preventWheelScroll = (e: WheelEvent) => {
      if (isDragging) {
        e.preventDefault()
      }
    }

    document.addEventListener('touchmove', preventTouchScroll, { passive: false })
    document.addEventListener('wheel', preventWheelScroll, { passive: false })

    // 存储事件监听器以便后续清理
    evt.target._preventTouchScroll = preventTouchScroll
    evt.target._preventWheelScroll = preventWheelScroll
  }

  const onDragEnd = (evt: any) => {
    isDragging = false

    // 清理事件监听器
    const preventTouchScroll = (evt.target as any)?._preventTouchScroll
    const preventWheelScroll = (evt.target as any)?._preventWheelScroll
    if (preventTouchScroll) {
      document.removeEventListener('touchmove', preventTouchScroll)
      delete (evt.target as any)._preventTouchScroll
    }
    if (preventWheelScroll) {
      document.removeEventListener('wheel', preventWheelScroll)
      delete (evt.target as any)._preventWheelScroll
    }

    // 如果有跨组拖拽，通知父组件处理
    if (evt.from !== evt.to) {
      // 跨组拖拽时，恢复原始数据，让父组件处理
      localDishes.value = [...props.group.dishList]

      const dragData = {
        fromGroupId: evt.from?.closest('.menu-group-edit')?.dataset.groupId,
        toGroupId: evt.to?.closest('.menu-group-edit')?.dataset.groupId,
        dishId: evt.item?.dataset.dishId,
        oldIndex: evt.oldIndex,
        newIndex: evt.newIndex
      }

      emit('crossGroupDrag', dragData)
    } else {
      // 组内排序
      emit('sortDishes', props.group.groupId, localDishes.value)
    }
  }
</script>

<style lang="stylus" scoped>
.menu-group-edit
  margin-bottom 20px
  padding 12px 16px 6px 16px
  border-radius 12px
  background-color #fff
  width 100%

.group-header-wrapper
  display flex
  align-items center
  gap 8px

  .group-drag-handle
    color rgba(0, 0, 0, 0.45)

.group-header
  flex 1
  display flex
  align-items center
  justify-content space-between

  .group-info
    display flex
    align-items center
    gap 8px
    .group-name
      font-size 14px
      color rgba(0, 0, 0, 0.8)
      font-weight 500
      line-height 20px
      overflow hidden
      text-overflow ellipsis
      white-space nowrap
    .group-info-icon
      width 16px
      height 16px
      color rgba(0, 0, 0, 0.45)

  .group-actions
    display flex
    align-items center
    .action-btn
      font-size 14px
      font-weight 400
      color rgba(0, 0, 0, 0.62)
      line-height 20px

.add-dish-btn
  font-size 14px
  font-weight 500
  display flex
  padding 6px 0
  color rgba(0, 0, 0, 0.62)
  gap 4px
  align-items center
  .add-dish-btn-text
    font-size 14px
    font-weight 500
    color rgba(0, 0, 0, 0.62)
    line-height 20px

// 拖拽状态样式
.dish-ghost
  opacity 0.4
  background rgba(0, 0, 0, 0.05)

.dish-chosen
  opacity 0.8
  background rgba(0, 0, 0, 0.02)

.dish-drag
  opacity 0.6
  background rgba(0, 0, 0, 0.02)
</style>
